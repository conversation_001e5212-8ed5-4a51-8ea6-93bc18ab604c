import { NextRequest, NextResponse } from "next/server";
import { tradingService } from "@/lib/trading-service";
import { ApiResponse } from "@/types/trading";

export async function GET(request: NextRequest) {
  try {
    // 检查交易服务是否已配置
    if (!tradingService.isConfigured()) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: "交易API未配置，请先在设置页面配置交易所API",
      }, { status: 400 });
    }

    // 获取账户信息
    const accountInfo = await tradingService.getAccountInfo();

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: accountInfo,
    });
  } catch (error: any) {
    console.error("获取账户信息失败:", error);
    
    let errorMessage = "获取账户信息失败";
    
    if (error.message.includes("API请求失败")) {
      errorMessage = "API请求失败，请检查API配置";
    } else if (error.message) {
      errorMessage = error.message;
    }

    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: errorMessage,
    }, { status: 500 });
  }
}
