import { NextRequest, NextResponse } from "next/server";
import { DataService } from "@/lib/data-service";
import { AIAnalysisService } from "@/lib/ai-analysis";
import { TradingStrategyService } from "@/lib/trading-strategy";
import {
  ApiResponse,
  AIAnalysisResult,
  TradingAdvice,
  PositionManagement,
  MarketAnalysisRequest,
} from "@/types/trading";

const dataService = DataService.getInstance();
const aiService = AIAnalysisService.getInstance();
const strategyService = TradingStrategyService.getInstance();

export async function POST(request: NextRequest) {
  try {
    const body: MarketAnalysisRequest = await request.json();
    const {
      symbol,
      riskTolerance = "MEDIUM",
      includeIndicators = true,
      openaiConfig,
    } = body;

    // 参数验证
    if (!symbol) {
      return NextResponse.json<ApiResponse<null>>(
        {
          success: false,
          error: "缺少必需参数: symbol",
        },
        { status: 400 }
      );
    }

    // 验证OpenAI配置
    if (!openaiConfig || !openaiConfig.apiKey || !openaiConfig.apiKey.trim()) {
      return NextResponse.json<ApiResponse<null>>(
        {
          success: false,
          error: "AI 服务未配置，请前往设置页面配置 OpenAI API",
        },
        { status: 400 }
      );
    }

    console.log(`开始分析 ${symbol}...`);

    // 1. 获取市场数据
    const marketData = await dataService.getMarketData(symbol);
    console.log(`获取到 ${symbol} 的市场数据`);

    // 2. 计算技术指标
    let technicalIndicators;
    if (includeIndicators) {
      try {
        technicalIndicators = dataService.calculateTechnicalIndicators(
          marketData.daily
        );
        console.log("技术指标计算完成");
      } catch (error) {
        console.error("技术指标计算失败:", error);
        return NextResponse.json<ApiResponse<null>>(
          {
            success: false,
            error: "技术指标计算失败，数据可能不足",
          },
          { status: 400 }
        );
      }
    } else {
      // 提供默认值
      const latestPrice =
        marketData.oneMin[marketData.oneMin.length - 1]?.close || 0;
      technicalIndicators = {
        rsi: 50,
        macd: { macd: 0, signal: 0, histogram: 0 },
        bollinger: {
          upper: latestPrice * 1.02,
          middle: latestPrice,
          lower: latestPrice * 0.98,
        },
        ema: { ema20: latestPrice, ema50: latestPrice, ema200: latestPrice },
        support: latestPrice * 0.95,
        resistance: latestPrice * 1.05,
      };
    }

    // 3. 执行AI分析
    console.log("开始AI分析...");
    const analysisResult = await aiService.analyzeMarketWithConfig(
      marketData,
      technicalIndicators,
      openaiConfig,
      riskTolerance
    );
    console.log("AI分析完成");

    // 4. 生成交易建议
    const tradingAdvice = strategyService.generateTradingAdvice(
      analysisResult,
      10000, // 默认账户余额
      riskTolerance
    );

    // 5. 生成滚仓策略
    const rollingStrategy = await aiService.generateRollingStrategy(
      marketData,
      analysisResult
    );

    // 6. 生成仓位管理策略
    const positionManagement = strategyService.generatePositionManagement(
      analysisResult,
      rollingStrategy
    );

    // 7. 计算风险收益比
    const riskReward = strategyService.calculateRiskReward(
      tradingAdvice.entryPrice,
      tradingAdvice.stopLoss,
      tradingAdvice.takeProfit
    );

    // 8. 趋势分析
    const trendAnalysis = strategyService.analyzeTrend(marketData.daily);

    const response = {
      analysis: analysisResult,
      advice: tradingAdvice,
      positionManagement,
      rollingStrategy,
      riskReward,
      trendAnalysis,
      marketData: {
        symbol: marketData.symbol,
        latestPrice:
          marketData.oneMin[marketData.oneMin.length - 1]?.close || 0,
        dailyChange:
          marketData.daily[marketData.daily.length - 1]?.changePercent || 0,
        volume24h: marketData.daily[marketData.daily.length - 1]?.volume || 0,
      },
    };

    console.log(`${symbol} 分析完成`);

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: response,
      message: "分析完成",
    });
  } catch (error) {
    console.error("AI分析失败:", error);

    // 根据错误类型返回不同的错误信息
    let errorMessage = "分析失败，请稍后重试";
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes("API key")) {
        errorMessage = "AI服务配置错误";
        statusCode = 500;
      } else if (error.message.includes("rate limit")) {
        errorMessage = "AI服务请求过于频繁，请稍后重试";
        statusCode = 429;
      } else if (error.message.includes("数据不足")) {
        errorMessage = "市场数据不足，无法进行分析";
        statusCode = 400;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json<ApiResponse<null>>(
      {
        success: false,
        error: errorMessage,
      },
      { status: statusCode }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get("symbol");

    if (!symbol) {
      return NextResponse.json<ApiResponse<null>>(
        {
          success: false,
          error: "缺少必需参数: symbol",
        },
        { status: 400 }
      );
    }

    // 快速分析（仅基于技术指标，不使用AI）
    const marketData = await dataService.getMarketData(symbol);
    const technicalIndicators = dataService.calculateTechnicalIndicators(
      marketData.daily
    );
    const trendAnalysis = strategyService.analyzeTrend(marketData.daily);

    // 简单的技术分析建议
    const latestPrice =
      marketData.oneMin[marketData.oneMin.length - 1]?.close || 0;
    let direction: "BUY" | "SELL" | "HOLD" = "HOLD";
    let confidence = 50;

    // 基于RSI和趋势的简单判断
    if (technicalIndicators.rsi < 30 && trendAnalysis.direction === "UP") {
      direction = "BUY";
      confidence = 70;
    } else if (
      technicalIndicators.rsi > 70 &&
      trendAnalysis.direction === "DOWN"
    ) {
      direction = "SELL";
      confidence = 70;
    }

    const quickAnalysis = {
      symbol: marketData.symbol,
      latestPrice,
      direction,
      confidence,
      rsi: technicalIndicators.rsi,
      trend: trendAnalysis,
      support: technicalIndicators.support,
      resistance: technicalIndicators.resistance,
      recommendation:
        direction === "HOLD"
          ? "当前信号不明确，建议观望"
          : `建议${
              direction === "BUY" ? "买入" : "卖出"
            }，信心度${confidence}%`,
    };

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: quickAnalysis,
      message: "快速分析完成",
    });
  } catch (error) {
    console.error("快速分析失败:", error);

    return NextResponse.json<ApiResponse<null>>(
      {
        success: false,
        error: error instanceof Error ? error.message : "快速分析失败",
      },
      { status: 500 }
    );
  }
}
