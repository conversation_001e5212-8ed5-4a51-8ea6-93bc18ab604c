# AI 加密货币交易分析平台

基于滚仓策略的智能交易分析平台，结合多时间维度数据和 AI 洞察，帮助您在加密货币市场中做出更明智的交易决策。

## 🚀 功能特性

### 📊 多维度数据分析

- **36 个月月线数据** - 长期趋势分析
- **30 天日线数据** - 中期趋势判断
- **7 天小时线数据** - 短期波动分析
- **24 小时 30 分钟数据** - 日内交易机会
- **1 小时分钟线数据** - 精确入场时机

### 🤖 AI 智能分析

- **GPT-4 驱动** - 基于最新 AI 技术的市场分析
- **技术指标计算** - RSI、MACD、布林带、EMA 等
- **趋势识别** - 自动识别短中长期趋势方向
- **风险评估** - 智能评估交易风险等级

### 💰 滚仓策略核心

- **复利增长** - 不是复利爆仓的科学方法
- **趋势确认** - 只在趋势确立时加仓
- **独立决策** - 每次加仓都是独立判断
- **严格止损** - 保护利润，控制回撤

### 🎯 精准交易建议

- **具体价位** - 明确的买入/卖出价格
- **仓位管理** - 科学的仓位分配建议
- **止损止盈** - 详细的风险控制策略
- **加仓计划** - 分步骤的仓位增加方案

## 🛠️ 技术栈

- **前端框架**: Next.js 15 + React 18
- **样式**: Tailwind CSS + shadcn/ui
- **语言**: TypeScript
- **AI 服务**: OpenAI GPT-4
- **图表**: Recharts
- **数据源**: 加密货币交易所 API

## 📦 安装和运行

### 环境要求

- Node.js 18+
- pnpm (推荐) 或 npm

### 1. 克隆项目

```bash
git clone <repository-url>
cd ai-trading-analysis
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 配置环境变量

```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，添加您的 OpenAI API 密钥：

```env
OPENAI_API_KEY=your_openai_api_key_here
```

### 4. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🎮 使用指南

### 1. 选择币种

- 在首页选择您要分析的加密货币
- 支持搜索功能，快速找到目标币种
- 显示热门交易对和实时价格

### 2. 查看图表

- 多时间维度 K 线图表
- 实时价格和成交量数据
- 技术指标可视化

### 3. AI 分析

- 点击"开始 AI 分析"按钮
- 选择风险偏好（保守/平衡/激进）
- 等待 AI 生成分析报告

### 4. 交易建议

- 查看具体的买入/卖出建议
- 了解仓位管理策略
- 获取风险控制方案

## 📋 滚仓策略原则

### ✅ 正确做法

- 只在趋势确立时加仓
- 每次加仓都是独立决策
- 严格回撤止损
- 3-5 倍底仓+浮动加仓策略
- 盈利保护止损
- 阶梯式减仓

### ❌ 错误做法

- 在震荡市中盲目加仓
- 冲动梭哈全部资金
- 不设置止损保护
- 让盈利变成亏损
- 贪婪不知止盈
- 情绪化交易决策

## ⚠️ 免责声明

本工具提供的分析和建议仅供参考，不构成投资建议。加密货币交易存在高风险，可能导致部分或全部资金损失。请根据自身情况谨慎决策，并做好风险管理。

**记住：市场不会奖励贪心，但一定会惩罚贪婪！**
