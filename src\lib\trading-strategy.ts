import {
  AIAnalysisResult,
  TradingAdvice,
  PositionManagement,
  RollingStrategy,
  TrendAnalysis,
  ProcessedKlineData,
} from "@/types/trading";

export class TradingStrategyService {
  private static instance: TradingStrategyService;

  static getInstance(): TradingStrategyService {
    if (!TradingStrategyService.instance) {
      TradingStrategyService.instance = new TradingStrategyService();
    }
    return TradingStrategyService.instance;
  }

  /**
   * 生成交易建议
   */
  generateTradingAdvice(
    analysisResult: AIAnalysisResult,
    accountBalance: number = 10000,
    riskTolerance: "LOW" | "MEDIUM" | "HIGH" = "MEDIUM"
  ): TradingAdvice {
    const { tradingSignal, marketCondition, keyLevels } = analysisResult;

    // 计算建议数量
    const riskPercentage = this.getRiskPercentage(riskTolerance);
    const riskAmount = accountBalance * (riskPercentage / 100);
    const stopLossDistance = Math.abs(
      tradingSignal.entryPrice - tradingSignal.stopLoss
    );
    const quantity = stopLossDistance > 0 ? riskAmount / stopLossDistance : 0;

    // 确定风险等级
    const riskLevel = this.assessRiskLevel(analysisResult);

    // 生成推理说明
    const reasoning = this.generateReasoning(analysisResult);

    return {
      action:
        tradingSignal.direction === "HOLD"
          ? "HOLD"
          : tradingSignal.direction === "LONG"
          ? "BUY"
          : "SELL",
      entryPrice: tradingSignal.entryPrice,
      quantity: Math.max(quantity, 0),
      stopLoss: tradingSignal.stopLoss,
      takeProfit: tradingSignal.takeProfit,
      timeframe: this.determineTimeframe(analysisResult),
      confidence: tradingSignal.confidence,
      reasoning,
      riskLevel,
    };
  }

  /**
   * 生成仓位管理策略
   */
  generatePositionManagement(
    analysisResult: AIAnalysisResult,
    rollingStrategy: RollingStrategy
  ): PositionManagement {
    const { tradingSignal, keyLevels } = analysisResult;

    // 计算加仓位置
    const addPositions = this.calculateAddPositions(
      tradingSignal.entryPrice,
      keyLevels,
      rollingStrategy
    );

    // 计算分批止盈
    const partialExits = this.calculatePartialExits(
      tradingSignal.entryPrice,
      tradingSignal.takeProfit,
      tradingSignal.direction
    );

    return {
      initialPosition: rollingStrategy.basePosition,
      addPositions,
      exitStrategy: {
        partialExits,
        stopLoss: tradingSignal.stopLoss,
        trailingStop: tradingSignal.confidence > 80,
      },
    };
  }

  /**
   * 分析趋势
   */
  analyzeTrend(data: ProcessedKlineData[]): TrendAnalysis {
    if (data.length < 20) {
      return {
        direction: "SIDEWAYS",
        strength: 1,
        duration: 0,
        breakoutPotential: 50,
        volumeConfirmation: false,
      };
    }

    // 计算趋势方向
    const recentData = data.slice(-20);
    const priceChange =
      recentData[recentData.length - 1].close - recentData[0].close;
    const priceChangePercent = (priceChange / recentData[0].close) * 100;

    let direction: "UP" | "DOWN" | "SIDEWAYS" = "SIDEWAYS";
    if (Math.abs(priceChangePercent) > 2) {
      direction = priceChangePercent > 0 ? "UP" : "DOWN";
    }

    // 计算趋势强度 (1-10)
    const strength = Math.min(Math.abs(priceChangePercent) / 2, 10);

    // 计算持续时间
    const duration = this.calculateTrendDuration(data);

    // 计算突破潜力
    const breakoutPotential = this.calculateBreakoutPotential(data);

    // 成交量确认
    const volumeConfirmation = this.checkVolumeConfirmation(data);

    return {
      direction,
      strength,
      duration,
      breakoutPotential,
      volumeConfirmation,
    };
  }

  /**
   * 检查滚仓条件
   */
  checkRollingConditions(
    analysisResult: AIAnalysisResult,
    currentPrice: number,
    entryPrice: number,
    currentPosition: number
  ): {
    shouldAdd: boolean;
    shouldReduce: boolean;
    addSize: number;
    reason: string;
  } {
    const { tradingSignal, marketTrend } = analysisResult;
    const priceChange = (currentPrice - entryPrice) / entryPrice;
    const isProfit =
      (tradingSignal.direction === "LONG" && priceChange > 0) ||
      (tradingSignal.direction === "SHORT" && priceChange < 0);

    // 加仓条件检查
    const shouldAdd =
      isProfit &&
      Math.abs(priceChange) > 0.02 && // 盈利超过2%
      tradingSignal.confidence > 70 &&
      marketTrend.shortTerm !== "NEUTRAL" &&
      currentPosition < 15; // 总仓位不超过15%

    // 减仓条件检查
    const shouldReduce = !isProfit && Math.abs(priceChange) > 0.03; // 亏损超过3%

    const addSize = shouldAdd ? 3 : 0; // 每次加仓3%

    let reason = "";
    if (shouldAdd) {
      reason = `趋势确认，盈利${(Math.abs(priceChange) * 100).toFixed(
        1
      )}%，建议加仓`;
    } else if (shouldReduce) {
      reason = `亏损${(Math.abs(priceChange) * 100).toFixed(1)}%，建议减仓止损`;
    } else {
      reason = "维持当前仓位，等待更好时机";
    }

    return {
      shouldAdd,
      shouldReduce,
      addSize,
      reason,
    };
  }

  /**
   * 计算风险收益比
   */
  calculateRiskReward(
    entryPrice: number,
    stopLoss: number,
    takeProfit: number[]
  ): number {
    const risk = Math.abs(entryPrice - stopLoss);
    const reward =
      takeProfit.length > 0 ? Math.abs(takeProfit[0] - entryPrice) : risk * 2;

    return risk > 0 ? reward / risk : 0;
  }

  /**
   * 私有方法：获取风险百分比
   */
  private getRiskPercentage(riskTolerance: string): number {
    const riskMap: Record<string, number> = {
      LOW: 1,
      MEDIUM: 2,
      HIGH: 3,
    };
    return riskMap[riskTolerance] || 2;
  }

  /**
   * 私有方法：评估风险等级
   */
  private assessRiskLevel(
    analysisResult: AIAnalysisResult
  ): "LOW" | "MEDIUM" | "HIGH" {
    const { marketCondition, tradingSignal } = analysisResult;

    let riskScore = 0;

    // 波动率风险
    if (marketCondition.volatility === "HIGH") riskScore += 2;
    else if (marketCondition.volatility === "MEDIUM") riskScore += 1;

    // 信心度风险
    if (tradingSignal.confidence < 60) riskScore += 2;
    else if (tradingSignal.confidence < 80) riskScore += 1;

    // 杠杆风险
    if (tradingSignal.leverage > 5) riskScore += 2;
    else if (tradingSignal.leverage > 2) riskScore += 1;

    if (riskScore >= 4) return "HIGH";
    if (riskScore >= 2) return "MEDIUM";
    return "LOW";
  }

  /**
   * 私有方法：生成推理说明
   */
  private generateReasoning(analysisResult: AIAnalysisResult): string {
    const { tradingSignal, marketTrend, marketCondition } = analysisResult;

    let reasoning = `基于多时间维度分析，`;

    // 趋势分析
    if (marketTrend.shortTerm === marketTrend.mediumTerm) {
      reasoning += `短中期趋势一致向${
        marketTrend.shortTerm === "BULLISH" ? "上" : "下"
      }，`;
    } else {
      reasoning += `短期${marketTrend.shortTerm}，中期${marketTrend.mediumTerm}，`;
    }

    // 市场条件
    reasoning += `当前市场波动率${marketCondition.volatility}，成交量${marketCondition.volume}。`;

    // 信心度
    if (tradingSignal.confidence > 80) {
      reasoning += `技术指标强烈支持该方向，建议积极参与。`;
    } else if (tradingSignal.confidence > 60) {
      reasoning += `技术指标支持该方向，建议谨慎参与。`;
    } else {
      reasoning += `信号不够明确，建议观望等待更好机会。`;
    }

    return reasoning;
  }

  /**
   * 私有方法：确定时间框架
   */
  private determineTimeframe(analysisResult: AIAnalysisResult): string {
    const { marketTrend, tradingSignal } = analysisResult;

    if (marketTrend.longTerm !== "NEUTRAL") {
      return "长期持有 (1-4周)";
    } else if (marketTrend.mediumTerm !== "NEUTRAL") {
      return "中期持有 (3-7天)";
    } else {
      return "短期交易 (数小时-1天)";
    }
  }

  /**
   * 私有方法：计算加仓位置
   */
  private calculateAddPositions(
    entryPrice: number,
    keyLevels: any,
    rollingStrategy: RollingStrategy
  ) {
    const addPositions = [];
    const threshold = rollingStrategy.additionThreshold / 100;

    // 向上加仓位置
    for (let i = 1; i <= 3; i++) {
      addPositions.push({
        price: entryPrice * (1 + threshold * i),
        size: 3, // 每次加仓3%
        condition: `价格突破${(entryPrice * (1 + threshold * i)).toFixed(
          4
        )}且成交量确认`,
      });
    }

    return addPositions;
  }

  /**
   * 私有方法：计算分批止盈
   */
  private calculatePartialExits(
    entryPrice: number,
    takeProfit: number[],
    direction: string
  ) {
    const partialExits = [];

    if (takeProfit.length > 0) {
      // 第一次止盈 - 30%仓位
      partialExits.push({
        price: takeProfit[0],
        percentage: 30,
      });

      // 第二次止盈 - 40%仓位
      if (takeProfit.length > 1) {
        partialExits.push({
          price: takeProfit[1],
          percentage: 40,
        });
      }

      // 第三次止盈 - 剩余30%
      if (takeProfit.length > 2) {
        partialExits.push({
          price: takeProfit[2],
          percentage: 30,
        });
      }
    }

    return partialExits;
  }

  /**
   * 私有方法：计算趋势持续时间
   */
  private calculateTrendDuration(data: ProcessedKlineData[]): number {
    // 简化实现：计算连续同方向变化的周期数
    let duration = 0;
    const recent = data.slice(-10);

    for (let i = recent.length - 1; i > 0; i--) {
      const currentChange = recent[i].changePercent;
      const prevChange = recent[i - 1].changePercent;

      if (
        (currentChange > 0 && prevChange > 0) ||
        (currentChange < 0 && prevChange < 0)
      ) {
        duration++;
      } else {
        break;
      }
    }

    return duration;
  }

  /**
   * 私有方法：计算突破潜力
   */
  private calculateBreakoutPotential(data: ProcessedKlineData[]): number {
    const recent = data.slice(-20);
    const priceRange =
      Math.max(...recent.map((d) => d.high)) -
      Math.min(...recent.map((d) => d.low));
    const currentPrice = recent[recent.length - 1].close;
    const rangePosition =
      (currentPrice - Math.min(...recent.map((d) => d.low))) / priceRange;

    // 接近区间边界时突破潜力更高
    if (rangePosition > 0.8 || rangePosition < 0.2) {
      return 80;
    } else if (rangePosition > 0.6 || rangePosition < 0.4) {
      return 60;
    } else {
      return 40;
    }
  }

  /**
   * 私有方法：检查成交量确认
   */
  private checkVolumeConfirmation(data: ProcessedKlineData[]): boolean {
    if (data.length < 5) return false;

    const recent = data.slice(-5);
    const avgVolume =
      recent.slice(0, -1).reduce((sum, d) => sum + d.volume, 0) / 4;
    const latestVolume = recent[recent.length - 1].volume;

    return latestVolume > avgVolume * 1.2; // 成交量放大20%以上
  }
}
