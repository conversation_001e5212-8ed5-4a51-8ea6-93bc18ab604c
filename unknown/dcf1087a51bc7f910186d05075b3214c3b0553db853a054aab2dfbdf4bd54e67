import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/data-service';
import { ApiResponse, MarketData } from '@/types/trading';

const dataService = DataService.getInstance();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get('symbol');
    const interval = searchParams.get('interval');
    const limit = searchParams.get('limit');

    // 参数验证
    if (!symbol) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: '缺少必需参数: symbol'
      }, { status: 400 });
    }

    // 如果请求单一时间维度的数据
    if (interval && limit) {
      const klineData = await dataService.getKlineData(
        symbol,
        interval,
        parseInt(limit),
      );

      return NextResponse.json<ApiResponse<any>>({
        success: true,
        data: klineData
      });
    }

    // 如果请求完整的市场数据（多时间维度）
    const marketData = await dataService.getMarketData(symbol);

    return NextResponse.json<ApiResponse<MarketData>>({
      success: true,
      data: marketData
    });

  } catch (error) {
    console.error('获取K线数据失败:', error);
    
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: error instanceof Error ? error.message : '获取K线数据失败'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { symbol, timeframes, startTime, endTime } = body;

    if (!symbol || !timeframes || !Array.isArray(timeframes)) {
      return NextResponse.json<ApiResponse<null>>({
        success: false,
        error: '请求参数格式错误'
      }, { status: 400 });
    }

    // 批量获取多个时间维度的数据
    const results: Record<string, any> = {};
    
    for (const timeframe of timeframes) {
      try {
        const limit = getDefaultLimit(timeframe);
        const data = await dataService.getKlineData(
          symbol,
          timeframe,
          limit,
          startTime,
          endTime
        );
        results[timeframe] = data;
      } catch (error) {
        console.error(`获取${timeframe}数据失败:`, error);
        results[timeframe] = null;
      }
    }

    return NextResponse.json<ApiResponse<any>>({
      success: true,
      data: results
    });

  } catch (error) {
    console.error('批量获取K线数据失败:', error);
    
    return NextResponse.json<ApiResponse<null>>({
      success: false,
      error: error instanceof Error ? error.message : '批量获取K线数据失败'
    }, { status: 500 });
  }
}

/**
 * 根据时间维度获取默认的数据条数
 */
function getDefaultLimit(interval: string): number {
  const limitMap: Record<string, number> = {
    '1M': 36,   // 36个月
    '1d': 30,   // 30天
    '1h': 168,  // 7天 * 24小时
    '30m': 48,  // 1天 * 48个30分钟
    '1m': 60,   // 1小时 * 60分钟
    '5m': 288,  // 1天 * 288个5分钟
    '15m': 96,  // 1天 * 96个15分钟
    '4h': 42,   // 7天 * 6个4小时
    '12h': 14,  // 7天 * 2个12小时
    '1w': 52,   // 52周
  };

  return limitMap[interval] || 100;
}
