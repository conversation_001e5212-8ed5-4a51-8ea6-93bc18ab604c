import { tradingConfigService } from "./trading-config-service";
import crypto from "crypto";

export interface OrderParams {
  symbol: string;
  side: "BUY" | "SELL";
  type: "LIMIT" | "MARKET" | "STOP" | "TAKE_PROFIT" | "STOP_MARKET" | "TAKE_PROFIT_MARKET";
  quantity?: string;
  price?: string;
  stopPrice?: string;
  timeInForce?: "GTC" | "IOC" | "FOK" | "GTX";
  reduceOnly?: boolean;
  positionSide?: "BOTH" | "LONG" | "SHORT";
}

export interface OrderResponse {
  orderId: number;
  symbol: string;
  status: string;
  clientOrderId: string;
  price: string;
  avgPrice: string;
  origQty: string;
  executedQty: string;
  cumQuote: string;
  timeInForce: string;
  type: string;
  reduceOnly: boolean;
  closePosition: boolean;
  side: string;
  positionSide: string;
  stopPrice: string;
  workingType: string;
  priceProtect: boolean;
  origType: string;
  updateTime: number;
}

export interface AccountInfo {
  feeTier: number;
  canTrade: boolean;
  canDeposit: boolean;
  canWithdraw: boolean;
  updateTime: number;
  totalWalletBalance: string;
  totalUnrealizedProfit: string;
  totalMarginBalance: string;
  totalPositionInitialMargin: string;
  totalOpenOrderInitialMargin: string;
  totalCrossWalletBalance: string;
  totalCrossUnPnl: string;
  availableBalance: string;
  maxWithdrawAmount: string;
  assets: Array<{
    asset: string;
    walletBalance: string;
    unrealizedProfit: string;
    marginBalance: string;
    maintMargin: string;
    initialMargin: string;
    positionInitialMargin: string;
    openOrderInitialMargin: string;
    crossWalletBalance: string;
    crossUnPnl: string;
    availableBalance: string;
    maxWithdrawAmount: string;
    marginAvailable: boolean;
    updateTime: number;
  }>;
  positions: Array<{
    symbol: string;
    initialMargin: string;
    maintMargin: string;
    unrealizedProfit: string;
    positionInitialMargin: string;
    openOrderInitialMargin: string;
    leverage: string;
    isolated: boolean;
    entryPrice: string;
    maxNotional: string;
    positionSide: string;
    positionAmt: string;
    notional: string;
    isolatedWallet: string;
    updateTime: number;
  }>;
}

export class TradingService {
  private static instance: TradingService;

  static getInstance(): TradingService {
    if (!TradingService.instance) {
      TradingService.instance = new TradingService();
    }
    return TradingService.instance;
  }

  /**
   * 检查交易服务是否已配置
   */
  isConfigured(): boolean {
    return tradingConfigService.isConfigured();
  }

  /**
   * 生成API签名
   */
  private generateSignature(queryString: string, secretKey: string): string {
    return crypto
      .createHmac("sha256", secretKey)
      .update(queryString)
      .digest("hex");
  }

  /**
   * 创建带签名的请求
   */
  private async makeSignedRequest(
    endpoint: string,
    method: "GET" | "POST" | "DELETE" = "GET",
    params: Record<string, any> = {}
  ): Promise<any> {
    const config = tradingConfigService.getConfig();
    
    if (!this.isConfigured()) {
      throw new Error("交易API未配置");
    }

    const timestamp = Date.now();
    const queryParams = { ...params, timestamp };
    
    // 构建查询字符串
    const queryString = Object.keys(queryParams)
      .sort()
      .map(key => `${key}=${queryParams[key]}`)
      .join("&");

    // 生成签名
    const signature = this.generateSignature(queryString, config.secretKey);
    
    // 构建完整URL
    const url = `${config.baseURL}${endpoint}?${queryString}&signature=${signature}`;

    const requestOptions: RequestInit = {
      method,
      headers: {
        "X-MBX-APIKEY": config.apiKey,
        "Content-Type": "application/json",
      },
    };

    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.msg || `API请求失败: ${response.status}`);
    }

    return response.json();
  }

  /**
   * 获取账户信息
   */
  async getAccountInfo(): Promise<AccountInfo> {
    return this.makeSignedRequest("/fapi/v2/account");
  }

  /**
   * 下单
   */
  async placeOrder(orderParams: OrderParams): Promise<OrderResponse> {
    return this.makeSignedRequest("/fapi/v1/order", "POST", orderParams);
  }

  /**
   * 测试下单（不会实际执行）
   */
  async testOrder(orderParams: OrderParams): Promise<OrderResponse> {
    return this.makeSignedRequest("/fapi/v1/order/test", "POST", orderParams);
  }

  /**
   * 查询订单
   */
  async getOrder(symbol: string, orderId?: number, origClientOrderId?: string): Promise<OrderResponse> {
    const params: any = { symbol };
    if (orderId) params.orderId = orderId;
    if (origClientOrderId) params.origClientOrderId = origClientOrderId;
    
    return this.makeSignedRequest("/fapi/v1/order", "GET", params);
  }

  /**
   * 撤销订单
   */
  async cancelOrder(symbol: string, orderId?: number, origClientOrderId?: string): Promise<OrderResponse> {
    const params: any = { symbol };
    if (orderId) params.orderId = orderId;
    if (origClientOrderId) params.origClientOrderId = origClientOrderId;
    
    return this.makeSignedRequest("/fapi/v1/order", "DELETE", params);
  }

  /**
   * 获取当前挂单
   */
  async getOpenOrders(symbol?: string): Promise<OrderResponse[]> {
    const params = symbol ? { symbol } : {};
    return this.makeSignedRequest("/fapi/v1/openOrders", "GET", params);
  }

  /**
   * 获取所有订单历史
   */
  async getAllOrders(symbol: string, orderId?: number, startTime?: number, endTime?: number, limit?: number): Promise<OrderResponse[]> {
    const params: any = { symbol };
    if (orderId) params.orderId = orderId;
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    if (limit) params.limit = limit;
    
    return this.makeSignedRequest("/fapi/v1/allOrders", "GET", params);
  }

  /**
   * 获取持仓信息
   */
  async getPositionRisk(symbol?: string): Promise<any[]> {
    const params = symbol ? { symbol } : {};
    return this.makeSignedRequest("/fapi/v2/positionRisk", "GET", params);
  }

  /**
   * 调整杠杆
   */
  async changeLeverage(symbol: string, leverage: number): Promise<any> {
    return this.makeSignedRequest("/fapi/v1/leverage", "POST", { symbol, leverage });
  }

  /**
   * 变换逐全仓模式
   */
  async changeMarginType(symbol: string, marginType: "ISOLATED" | "CROSSED"): Promise<any> {
    return this.makeSignedRequest("/fapi/v1/marginType", "POST", { symbol, marginType });
  }

  /**
   * 获取交易手续费率
   */
  async getTradingStatus(): Promise<any> {
    return this.makeSignedRequest("/fapi/v1/commissionRate", "GET");
  }
}

export const tradingService = TradingService.getInstance();
