import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";
import { OpenAIConfig } from "@/types/trading";

export async function POST(request: NextRequest) {
  try {
    const config: OpenAIConfig = await request.json();

    // 验证配置
    if (!config.apiKey || !config.baseURL || !config.model) {
      return NextResponse.json({
        success: false,
        error: "配置信息不完整",
      });
    }

    // 创建 OpenAI 客户端
    const openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
    });

    // 测试 API 连接
    try {
      const completion = await openai.chat.completions.create({
        model: config.model,
        messages: [
          {
            role: "user",
            content: "Hello, this is a test message. Please respond with 'OK'.",
          },
        ],
        max_tokens: 10,
        temperature: 0,
      });

      if (completion.choices && completion.choices.length > 0) {
        return NextResponse.json({
          success: true,
          message: "API 配置测试成功",
          response: completion.choices[0].message?.content,
        });
      } else {
        return NextResponse.json({
          success: false,
          error: "API 响应格式异常",
        });
      }
    } catch (apiError: any) {
      console.error("API 测试失败:", apiError);
      
      let errorMessage = "API 调用失败";
      
      if (apiError.status === 401) {
        errorMessage = "API Key 无效或已过期";
      } else if (apiError.status === 404) {
        errorMessage = "模型不存在或 Base URL 错误";
      } else if (apiError.status === 429) {
        errorMessage = "API 调用频率限制";
      } else if (apiError.message) {
        errorMessage = apiError.message;
      }

      return NextResponse.json({
        success: false,
        error: errorMessage,
      });
    }
  } catch (error: any) {
    console.error("配置测试错误:", error);
    return NextResponse.json({
      success: false,
      error: "服务器内部错误: " + error.message,
    });
  }
}
