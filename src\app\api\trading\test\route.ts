import { NextRequest, NextResponse } from "next/server";
import { TradingAPIConfig } from "@/types/trading";
import crypto from "crypto";

export async function POST(request: NextRequest) {
  try {
    const config: TradingAPIConfig = await request.json();

    // 验证配置
    if (!config.apiKey || !config.secretKey || !config.baseURL) {
      return NextResponse.json({
        success: false,
        error: "配置信息不完整",
      });
    }

    // 测试API连接 - 获取账户信息
    try {
      const timestamp = Date.now();
      const queryString = `timestamp=${timestamp}`;

      // 生成签名
      const signature = crypto
        .createHmac("sha256", config.secretKey)
        .update(queryString)
        .digest("hex");

      // 构建请求URL
      const url = `${config.baseURL}/fapi/v2/account?${queryString}&signature=${signature}`;

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "X-MBX-APIKEY": config.apiKey,
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const accountInfo = await response.json();

        return NextResponse.json({
          success: true,
          message: "交易API配置测试成功",
          accountInfo: {
            totalWalletBalance: accountInfo.totalWalletBalance,
            totalUnrealizedProfit: accountInfo.totalUnrealizedProfit,
            totalMarginBalance: accountInfo.totalMarginBalance,
            availableBalance: accountInfo.availableBalance,
            maxWithdrawAmount: accountInfo.maxWithdrawAmount,
            assets: accountInfo.assets?.slice(0, 3), // 只返回前3个资产信息
          },
        });
      } else {
        const errorData = await response.json().catch(() => ({}));

        let errorMessage = "API调用失败";

        if (response.status === 401) {
          errorMessage = "API Key 或 Secret Key 无效";
        } else if (response.status === 403) {
          errorMessage = "API权限不足，请检查API Key权限设置";
        } else if (response.status === 404) {
          errorMessage = "API接口不存在，请检查Base URL是否正确";
        } else if (response.status === 429) {
          errorMessage = "API调用频率限制";
        } else if (errorData.msg) {
          errorMessage = errorData.msg;
        }

        return NextResponse.json({
          success: false,
          error: errorMessage,
        });
      }
    } catch (apiError: any) {
      console.error("交易API测试失败:", apiError);

      let errorMessage = "API调用失败";

      if (apiError.name === "TypeError" && apiError.message.includes("fetch")) {
        errorMessage = "网络连接失败，请检查Base URL是否正确";
      } else if (apiError.message) {
        errorMessage = apiError.message;
      }

      return NextResponse.json({
        success: false,
        error: errorMessage,
      });
    }
  } catch (error: any) {
    console.error("交易API配置测试错误:", error);
    return NextResponse.json({
      success: false,
      error: "服务器内部错误: " + error.message,
    });
  }
}
