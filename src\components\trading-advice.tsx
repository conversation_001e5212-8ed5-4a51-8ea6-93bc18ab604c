"use client";

import React from "react";
import {
  TrendingUp,
  TrendingDown,
  Target,
  Shield,
  Clock,
  AlertTriangle,
  DollarSign,
  BarChart3,
  Copy,
  ExternalLink,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

interface TradingAdviceProps {
  advice: {
    action: "BUY" | "SELL" | "HOLD";
    entryPrice: number;
    quantity: number;
    stopLoss: number;
    takeProfit: number[];
    timeframe: string;
    confidence: number;
    reasoning: string;
    riskLevel: "LOW" | "MEDIUM" | "HIGH";
  };
  positionManagement: {
    initialPosition: number;
    addPositions: Array<{
      price: number;
      size: number;
      condition: string;
    }>;
    exitStrategy: {
      partialExits: Array<{
        price: number;
        percentage: number;
      }>;
      stopLoss: number;
      trailingStop: boolean;
    };
  };
  rollingStrategy: {
    basePosition: number;
    additionThreshold: number;
    maxPosition: number;
    profitTarget: number;
    stopLoss: number;
    trendConfirmation: boolean;
  };
  riskReward: number;
  symbol: string;
  className?: string;
}

export function TradingAdvice({
  advice,
  positionManagement,
  rollingStrategy,
  riskReward,
  symbol,
  className,
}: TradingAdviceProps) {
  const getActionColor = (action: string) => {
    switch (action) {
      case "BUY":
        return "bg-green-500 hover:bg-green-600";
      case "SELL":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case "BUY":
        return <TrendingUp className="h-4 w-4" />;
      case "SELL":
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case "LOW":
        return "text-green-500 bg-green-50";
      case "MEDIUM":
        return "text-yellow-600 bg-yellow-50";
      case "HIGH":
        return "text-red-500 bg-red-50";
      default:
        return "text-gray-500 bg-gray-50";
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("📋 复制成功", {
        description: `${label}已复制到剪贴板`,
        duration: 2000,
      });
    } catch (error) {
      toast.error("❌ 复制失败", {
        description: "无法访问剪贴板，请手动复制",
        duration: 3000,
      });
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(4);
    } else if (price >= 0.01) {
      return price.toFixed(6);
    } else {
      return price.toFixed(8);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 主要交易建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>交易建议 - {symbol}</span>
            </div>
            <Badge className={`${getActionColor(advice.action)} text-white`}>
              {getActionIcon(advice.action)}
              <span className="ml-1">
                {advice.action === "BUY"
                  ? "买入"
                  : advice.action === "SELL"
                  ? "卖出"
                  : "观望"}
              </span>
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 信心度和风险等级 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">信心度</span>
              <Progress value={advice.confidence} className="w-24" />
              <span className="text-sm font-medium">{advice.confidence}%</span>
            </div>
            <Badge className={getRiskColor(advice.riskLevel)}>
              {advice.riskLevel === "LOW"
                ? "低风险"
                : advice.riskLevel === "MEDIUM"
                ? "中等风险"
                : "高风险"}
            </Badge>
          </div>

          <Separator />

          {/* 关键价格信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">入场价格</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold">
                  ${formatPrice(advice.entryPrice)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(advice.entryPrice.toString(), "入场价格")
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">止损价格</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold text-red-500">
                  ${formatPrice(advice.stopLoss)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(advice.stopLoss.toString(), "止损价格")
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">止盈目标</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold text-green-500">
                  ${formatPrice(advice.takeProfit[0] || 0)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(
                      (advice.takeProfit[0] || 0).toString(),
                      "止盈价格"
                    )
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">风险收益比</p>
              <p className="font-semibold">1:{riskReward.toFixed(1)}</p>
            </div>
          </div>

          {/* 时间框架和数量 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">持有时间:</span>
              <span className="font-medium">{advice.timeframe}</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">建议数量:</span>
              <span className="font-medium">{advice.quantity.toFixed(4)}</span>
            </div>
          </div>

          {/* 推理说明 */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">{advice.reasoning}</p>
          </div>
        </CardContent>
      </Card>

      {/* 滚仓策略详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>滚仓策略</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 策略概览 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">基础仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.basePosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">最大仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.maxPosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">加仓阈值</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.additionThreshold}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">趋势确认</p>
              <Badge
                variant={
                  rollingStrategy.trendConfirmation ? "default" : "secondary"
                }
              >
                {rollingStrategy.trendConfirmation ? "已确认" : "待确认"}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* 加仓计划 */}
          <div>
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>加仓计划</span>
            </h4>
            <div className="space-y-2">
              {positionManagement.addPositions
                .slice(0, 3)
                .map((position, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <span className="text-sm font-medium">
                      加仓 {index + 1}
                    </span>
                    <span className="text-sm">
                      ${formatPrice(position.price)}
                    </span>
                    <Badge variant="outline">{position.size}%</Badge>
                    <span className="text-xs text-gray-500 max-w-32 truncate">
                      {position.condition}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          <Separator />

          {/* 分批止盈 */}
          <div>
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <Target className="h-4 w-4" />
              <span>分批止盈</span>
            </h4>
            <div className="space-y-2">
              {positionManagement.exitStrategy.partialExits.map(
                (exit, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-green-50 rounded"
                  >
                    <span className="text-sm font-medium">
                      止盈 {index + 1}
                    </span>
                    <span className="text-sm">${formatPrice(exit.price)}</span>
                    <Badge variant="outline" className="text-green-600">
                      {exit.percentage}%
                    </Badge>
                  </div>
                )
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 风险提示 */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>风险提示:</strong>
          滚仓策略需要严格的纪律性和风险控制。请确保： 1) 严格按照止损位执行；
          2) 不要在震荡市中盲目加仓； 3) 保护已有利润，避免倒亏； 4)
          根据市场变化及时调整策略。
        </AlertDescription>
      </Alert>

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <Button
          className={getActionColor(advice.action)}
          disabled={advice.action === "HOLD"}
        >
          {getActionIcon(advice.action)}
          <span className="ml-2">
            {advice.action === "BUY"
              ? "执行买入"
              : advice.action === "SELL"
              ? "执行卖出"
              : "暂时观望"}
          </span>
        </Button>
        <Button variant="outline">
          <ExternalLink className="h-4 w-4 mr-2" />
          查看详细分析
        </Button>
        <Button variant="outline">
          <Copy className="h-4 w-4 mr-2" />
          复制策略
        </Button>
      </div>
    </div>
  );
}
